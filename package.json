{"name": "fabrication-app", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:https": "next dev --experimental-https", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"html5-qrcode": "^2.3.8", "jotai": "^2.12.3", "next": "15.3.1", "next-pwa": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "eslint": "^9", "eslint-config-next": "15.3.1", "local-ssl-proxy": "^2.0.5", "tailwindcss": "^4", "typescript": "^5"}}