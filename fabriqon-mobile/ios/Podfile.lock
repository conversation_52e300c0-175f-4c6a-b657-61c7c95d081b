PODS:
  - Flutter (1.0.0)
  - MTBBarcodeScanner (5.0.11)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - MTBBarcodeScanner

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  path_provider_foundation: eaf5b3e458fc0e5fbb9940fb09980e853fe058b8
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4

PODFILE CHECKSUM: 30a1617d810dee61c21b4f952f46cb788384d655

COCOAPODS: 1.12.1
