import 'package:fabrication/screens/confirmation_screen.dart';
import 'package:fabrication/screens/main_screen.dart';
import 'package:fabrication/screens/qr_scan_screen.dart';
// import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:get_storage/get_storage.dart';

void main() async {
  await GetStorage.init();
  // await Firebase.initializeApp();
  runApp(const Fabrication());
}

final router = GoRouter(
  initialLocation: "/main",
  routes: [
    GoRoute(
      path: '/',
      builder: (_, __) => const MainScreen(),
      routes: [
        GoRoute(
          path: 'main',
          builder: (_, __) => const MainScreen(),
        ),
        GoRoute(
          path: 'pairing/qr_scan',
          builder: (_, __) => const QRScanScreen(),
        ),
        GoRoute(
          path: 'pairing/confirmation/:token',
          builder: (context, state) => ConfirmationPage(initiationToken: state.pathParameters['token']),
        ),
      ],
    ),
  ],
);

class Fabrication extends StatelessWidget {
  const Fabrication({super.key});

  static const MaterialColor white = MaterialColor(
    0xFFFFFFFF,
    <int, Color>{
      50: Color(0xFFFFFFFF),
      100: Color(0xFFFFFFFF),
      200: Color(0xFFFFFFFF),
      300: Color(0xFFFFFFFF),
      400: Color(0xFFFFFFFF),
      500: Color(0xFFFFFFFF),
      600: Color(0xFFFFFFFF),
      700: Color(0xFFFFFFFF),
      800: Color(0xFFFFFFFF),
      900: Color(0xFFFFFFFF),
    },
  );

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: "Fabrication",
      theme: ThemeData(
        primarySwatch: white,
      ),
      routerConfig: router,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
    );
  }
}
