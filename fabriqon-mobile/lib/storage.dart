import 'package:get_storage/get_storage.dart';

enum DataKeys {API_TOKEN, EMPLOYEE_NAME}

class Storage {
  static final storage = GetStorage();

  static Future<String> read(String key) async {
    var value = await storage.read(key);
    if (value == null) {
      throw Exception("value with key [$key] not found");
    }
    return value;
  }

  static Future<void> delete(String key) async {
    await storage.remove(key);
  }

  static Future<void> deleteAll() async {
    await storage.erase();
  }

  static Future<void> write(String key, dynamic value) async {
    await storage.write(key, value);
  }
}
