import 'package:flutter/material.dart';

import '../styles.dart';

class ErrorPage extends StatelessWidget {
  const ErrorPage(
      {super.key, required this.message, this.buttonText, this.buttonPressed});

  final String message;
  final String? buttonText;
  final Function? buttonPressed;

  _callback() {
    buttonPressed!.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(top: 100),
        child: buttonText == null
            ? Text(
                message,
                style: const TextStyle(fontSize: 24),
              )
            : Column(
                children: [
                  Text(message, style: const TextStyle(fontSize: 24)),
                  Container(
                    padding: const EdgeInsets.only(top: 200),
                    child: SizedB<PERSON>(
                      height: 55,
                      width: 200,
                      child: ElevatedButton(
                        onPressed: () => _callback(),
                        style: const ButtonStyle(
                            backgroundColor:
                                MaterialStatePropertyAll<Color>(yellowButton)),
                        child: Text(buttonText!,
                            style: const TextStyle(color: Colors.white, fontSize: 18)),
                      ),
                    ),
                  ),
                ],
              ));
  }
}
