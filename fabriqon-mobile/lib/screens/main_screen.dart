import 'dart:io';

import 'package:fabrication/api/manufacturing.dart';
import 'package:fabrication/screens/tasks.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../api/pairing.dart';
import '../storage.dart';
import '../styles.dart';
import '../utils.dart';

// @pragma('vm:entry-point')
// Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   //TODO redirect to _handleMessage or something
// }

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  String employeeName = '';
  DateTime selectedDate = DateTime.now();
  List<Task> tasks = List.empty();

  void _previousDay() {
    setState(() {
      selectedDate = selectedDate.subtract(const Duration(days: 1));
    });
    _tasks();
  }

  void _nextDay() {
    setState(() {
      selectedDate = selectedDate.add(const Duration(days: 1));
    });
    _tasks();
  }

  String _formatDate(DateTime dateTime) {
    return DateFormat('EEEE, MMMM d', Platform.localeName).format(dateTime);
  }

  _tasks() {
    Api()
        .getTasks(selectedDate)
        .then((value) => {
              setState(() {
                tasks = value;
              })
            })
        .catchError((error) {});
  }

  _employee() {
    Storage.read(DataKeys.EMPLOYEE_NAME.name)
        .then((value) => {
              setState(() {
                employeeName = value;
              })
            })
        .catchError((_, __) => {GoRouter.of(context).go("/pairing/qr_scan")});
  }

  Future<void> _logout() async {
    return PairingApi().logout().whenComplete(() => Storage.deleteAll());
  }

  // Future<void> _setupInteractedMessage() async {
  //   RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
  //   if (initialMessage != null) {
  //     _handleMessage(initialMessage);
  //   }
  //   FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
  // }
  //
  // _handleMessage(RemoteMessage message) {
  //   //todo
  // }

  @override
  void initState() {
    super.initState();
    _employee();
    _tasks();

    //firebase messaging
    // _setupInteractedMessage();
    // FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    // FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    //   _handleMessage(message);
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red,
          ),
          margin: const EdgeInsets.all(8.0),
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: Text(
            Utils.getInitials(employeeName),
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
        title: Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.zero,
            child: Text(
              employeeName,
              style: const TextStyle(fontSize: 14),
            )),
      ),
      body: Container(
        padding: const EdgeInsets.all(20.0),
        child: Container(
          alignment: Alignment.topCenter,
          child: SingleChildScrollView(
            child: Column(
              children: <Widget>[
                Container(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          flex: 0,
                          child: IconButton(
                            icon: const Icon(Icons.chevron_left),
                            onPressed: _previousDay,
                          ),
                        ),
                        Expanded(
                          child: Center(
                            child: Text(
                              _formatDate(selectedDate),
                              style: const TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 0,
                          child: IconButton(
                            icon: const Icon(Icons.chevron_right),
                            onPressed: _nextDay,
                          ),
                        ),
                      ],
                    )),
                Column(
                  children: tasks
                      .where((element) => element.status != Status.DONE)
                      .map((e) => TaskCard(task: e, taskStatusCallback: _tasks))
                      .toList(),
                ),
                const Divider(),
                Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      AppLocalizations.of(context)!.completedTasks,
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    )),
                Column(
                  children: tasks
                      .where((element) => element.status == Status.DONE)
                      .map((e) => TaskCard(task: e, taskStatusCallback: _tasks))
                      .toList(),
                ),
              ],
            ),
          ),
        ),
      ),
      endDrawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            SizedBox(
              height: kToolbarHeight + MediaQuery.of(context).padding.top,
              child: DrawerHeader(
                margin: EdgeInsets.zero,
                decoration: const BoxDecoration(
                  color: yellowButton,
                ),
                child:
                    Text(AppLocalizations.of(context)!.menu, style: const TextStyle(fontSize: 22, color: Colors.white)),
              ),
            ),
            ListTile(
              title: Text(AppLocalizations.of(context)!.logout, style: const TextStyle(fontSize: 18)),
              onTap: () {
                _logout().then((value) => GoRouter.of(context).go("/pairing/qr_scan"));
              },
            ),
          ],
        ),
      ),
    );
  }
}
