import 'package:fabrication/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../api/manufacturing.dart';

class TaskCard extends StatefulWidget {
  const TaskCard({super.key, required this.task, required this.taskStatusCallback});

  final Task task;
  final Function taskStatusCallback;

  @override
  State<TaskCard> createState() => _TaskCardState();
}

class _TaskCardState extends State<TaskCard> {
  bool _expanded = false;

  void _toggleExpanded() {
    setState(() {
      _expanded = !_expanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      shape: RoundedRectangleBorder(side: BorderSide.none, borderRadius: BorderRadius.circular(8.0)),
      child: Container(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Row(children: <Widget>[
              Expanded(
                child: Text(
                  widget.task.number,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
              Expanded(
                child: Text(
                  widget.task.assignedWorkstations.isEmpty ? "" : widget.task.assignedWorkstations.elementAt(0).name,
                  textAlign: TextAlign.right,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ]),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 0.0),
              child: Row(children: [
                Expanded(
                  child: Text(
                    widget.task.product.name,
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ),
                Expanded(
                  flex: 0,
                  child: IconButton(
                    icon: Icon(_expanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down),
                    tooltip: "Expand materials",
                    onPressed: _toggleExpanded,
                  ),
                )
              ]),
            ),
            Row(children: [Text("${widget.task.name} • ${widget.task.quantity} ${_measurementUnitText(widget.task.measurementUnit.name)}")]),
            !_expanded ? const Divider() : _materials(widget.task),
            Container(
              alignment: Alignment.centerLeft,
              child: Column(
                children: [
                  Text(
                    AppLocalizations.of(context)!.notes,
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  Text(widget.task.notes ?? "", style: const TextStyle(height: 2))
                ],
              ),
            ),
            const Divider(),
            if (widget.task.status == Status.STOPPED)
              Container(
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.only(top: 10, bottom: 10),
                child: Text(_stoppedReasonText(widget.task.statusReason!)),
              ),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: _actionButtons(widget.task)),
          ],
        ),
      ),
    );
  }

  List<Widget> _actionButtons(Task e) {
    if (e.status == Status.TODO || e.status == Status.STOPPED) {
      return List.of([
        Expanded(
          child: FilledButton(
            onPressed: () => _taskInProgress(e),
            style: ButtonStyle(
                backgroundColor: const MaterialStatePropertyAll<Color>(greenButton),
                shape: MaterialStatePropertyAll<RoundedRectangleBorder>(
                    RoundedRectangleBorder(side: BorderSide.none, borderRadius: BorderRadius.circular(8.0)))),
            child: Text(
              e.status == Status.TODO ? AppLocalizations.of(context)!.start : AppLocalizations.of(context)!.resume,
              style: const TextStyle(fontSize: 14, color: Colors.white),
            ),
          ),
        )
      ]);
    }
    if (e.status == Status.IN_PROGRESS) {
      return List.of([
        FilledButton(
          onPressed: () => _stoppedOptions(e),
          style: ButtonStyle(
              backgroundColor: const MaterialStatePropertyAll<Color>(redButton),
              shape: MaterialStatePropertyAll<RoundedRectangleBorder>(
                  RoundedRectangleBorder(side: BorderSide.none, borderRadius: BorderRadius.circular(8.0)))),
          child: Text(
            AppLocalizations.of(context)!.stop,
            style: const TextStyle(fontSize: 14, color: Colors.white),
          ),
        ),
        FilledButton(
          onPressed: () => _taskDone(e),
          style: ButtonStyle(
              backgroundColor: const MaterialStatePropertyAll<Color>(greenButton),
              shape: MaterialStatePropertyAll<RoundedRectangleBorder>(
                  RoundedRectangleBorder(side: BorderSide.none, borderRadius: BorderRadius.circular(8.0)))),
          child: Text(
            AppLocalizations.of(context)!.markCompleted,
            style: const TextStyle(fontSize: 14, color: Colors.white),
          ),
        ),
      ]);
    }
    return List.empty();
  }

  Widget _materials(Task task) {
    return Container(
      alignment: Alignment.centerLeft,
      child: Column(children: [
        const Divider(),
        Row(children: [
          Expanded(
              child: Text(
            AppLocalizations.of(context)!.materials,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          )),
          Expanded(
            flex: 0,
            child: Text(
              AppLocalizations.of(context)!.neededQuantity,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ),
        ]),
        Container(
            padding: const EdgeInsets.only(top: 10),
            child: Column(
              children: _materialAndQuantity(task.materials),
            )),
      ]),
    );
  }

  List<Widget> _materialAndQuantity(List<ManufacturingMaterial> materials) {
    return materials
        .map((e) => Column(
              children: [
                Row(children: [
                  Expanded(
                      child: Text(
                    e.name,
                    style: const TextStyle(fontSize: 14),
                  )),
                  Expanded(
                    flex: 0,
                    child: Text(
                      e.quantity.toString(),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ]),
                const Divider()
              ],
            ))
        .toList();
  }

  _taskInProgress(Task task) {
    Api().inProgress(task.id).then((value) => widget.taskStatusCallback.call()).catchError((error) {
      ScaffoldMessenger.of(context).showSnackBar(_snackBar(AppLocalizations.of(context)!.cantUpdateTask));
      widget.taskStatusCallback.call();
    });
  }

  _taskDone(Task task) {
    Api().done(task.id).then((value) => widget.taskStatusCallback.call()).catchError((error) {
      ScaffoldMessenger.of(context).showSnackBar(_snackBar(AppLocalizations.of(context)!.cantUpdateTask));
      widget.taskStatusCallback.call();
    });
  }

  _taskBlocked(Task task, StatusReason reason) {
    Api().stopped(task.id, reason).then((value) => widget.taskStatusCallback.call()).catchError((error) {
      ScaffoldMessenger.of(context).showSnackBar(_snackBar(AppLocalizations.of(context)!.cantUpdateTask));
      widget.taskStatusCallback.call();
    });
  }

  Future<void> _stoppedOptions(Task task) async {
    var reason = await showDialog<StatusReason>(
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            alignment: Alignment.bottomCenter,
            // contentPadding: const EdgeInsets.all(0),
            insetPadding: const EdgeInsets.only(left: 25, right: 25, bottom: 25),
            shape: RoundedRectangleBorder(side: BorderSide.none, borderRadius: BorderRadius.circular(8.0)),
            children: [
              _blockedOptionButton(AppLocalizations.of(context)!.paused, Icons.pause, () {
                Navigator.pop(context, StatusReason.PAUSED);
              }),
              _blockedOptionButton(AppLocalizations.of(context)!.missingMaterial, Icons.add, () {
                Navigator.pop(context, StatusReason.MISSING_MATERIAL);
              }),
              _blockedOptionButton(AppLocalizations.of(context)!.brokenEquipment, Icons.broken_image_outlined, () {
                Navigator.pop(context, StatusReason.BROKEN_EQUIPMENT);
              }),
              _blockedOptionButton(AppLocalizations.of(context)!.equipmentUnavailable, Icons.not_interested, () {
                Navigator.pop(context, StatusReason.EQUIPMENT_UNAVAILABLE);
              }),
            ],
          );
        });
    if (reason != null) {
      _taskBlocked(task, reason);
    }
  }

  Widget _blockedOptionButton(String button, IconData icon, VoidCallback onPressed) {
    return SizedBox(
      width: MediaQuery.of(context).size.width + 200,
      child: ElevatedButton.icon(
        icon: Icon(icon),
        style: ElevatedButton.styleFrom(elevation: 0, minimumSize: const Size.fromHeight(50)),
        onPressed: onPressed,
        label: Align(
          alignment: Alignment.centerLeft,
          child: Text(
            button,
            style: const TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  SnackBar _snackBar(String message) {
    return SnackBar(
      content: Text(message),
    );
  }

  String _stoppedReasonText(StatusReason statusReason) {
    switch (statusReason) {
      case StatusReason.PAUSED: return AppLocalizations.of(context)!.paused;
      case StatusReason.MISSING_MATERIAL: return AppLocalizations.of(context)!.missingMaterial;
      case StatusReason.BROKEN_EQUIPMENT: return AppLocalizations.of(context)!.brokenEquipment;
      case StatusReason.EQUIPMENT_UNAVAILABLE: return AppLocalizations.of(context)!.equipmentUnavailable;
    }
  }

  String _measurementUnitText(String name) {
    switch (name) {
      case "pcs": return AppLocalizations.of(context)!.measurementUnitPiece;
      default: return name;
    }
  }
}
