import 'package:fabrication/api/pairing.dart';
import 'package:fabrication/screens/general_error.dart';
import 'package:fabrication/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

// import '../api/firebase.dart';
import '../config.dart';
import '../storage.dart';
import '../styles.dart';

class ConfirmationPage extends StatefulWidget {
  const ConfirmationPage(
      {super.key, required this.initiationToken});

  final String? initiationToken;

  @override
  State<ConfirmationPage> createState() => _ConfirmationPage();
}

class _ConfirmationPage extends State<ConfirmationPage> {
  PairingInit? pairingInit;
  bool apiError = false;
  String? apiErrorMsg;

  _loadPairingInitToken() {
    PairingApi()
        .initiate(widget.initiationToken ?? "")
        .then((value) => setState(() {
              apiError = false;
              pairingInit = value;
            }))
        .catchError((err) => setState(() {
              apiError = true;
              apiErrorMsg = err.toString();
            }));
  }

  @override
  void initState() {
    super.initState();
    _loadPairingInitToken();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.initiationToken == null) {
      return ErrorPage(
        message: AppLocalizations.of(context)!.initiationTokenIsNull,
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.accountConfirmation),
        // automaticallyImplyLeading: false,
      ),
      body: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.only(top: 80),
        child: apiError
            ? _error(apiErrorMsg)
            : pairingInit != null
                ? _confirmation()
                : _loading(),
      ),
    );
  }

  _confirmation() {
    return Column(
      children: [
        Container(
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red,
          ),
          margin: const EdgeInsets.all(14.0),
          padding: const EdgeInsets.all(14.0),
          alignment: Alignment.center,
          child: Text(
            Utils.getInitials(pairingInit!.employeeName),
            style: const TextStyle(color: Colors.white, fontSize: 18),
          ),
        ),
        Text(
          pairingInit!.employeeName,
          style: const TextStyle(color: Colors.black, fontSize: 24),
        ),
        Container(
          padding: const EdgeInsets.only(top: 100, left: 20, right: 20),
          child: Column(
            children: [
              Text(
                  AppLocalizations.of(context)!.confirmationOfTAndC,
                  textAlign: TextAlign.center,
              ),
              InkWell(
                  child: Text(AppLocalizations.of(context)!.privacyPolicy, style: const TextStyle(color: Colors.blue, height: 2)),
                  onTap: () => _launchUrl('https://fabriqon-app-prod.web.app/privacy_policy_en.html')
              ),
              InkWell(
                  child: Text(AppLocalizations.of(context)!.tAndC, style: const TextStyle(color: Colors.blue, height: 2)),
                  onTap: () => _launchUrl('https://fabriqon-app-prod.web.app/t_and_c_en.html')
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.only(top: 40),
          child: SizedBox(
            height: 55,
            width: 200,
            child: ElevatedButton(
              style: const ButtonStyle(
                backgroundColor: MaterialStatePropertyAll<Color>(yellowButton),
              ),
              onPressed: () {
                PairingApi()
                    .confirm(pairingInit!.confirmationToken)
                    .then((token) {
                  Storage.write(DataKeys.API_TOKEN.name, token.token);
                  Storage.write(
                      DataKeys.EMPLOYEE_NAME.name, pairingInit!.employeeName);
                  // FirebaseTokens.initiate(token.token);
                  GoRouter.of(context).go("/main");
                }).catchError((error) {
                  GoRouter.of(context).go("/");
                });
              },
              child: Text(
                AppLocalizations.of(context)!.confirmAccount,
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _error(String? errorMsg) {
    return ErrorPage(
      message: AppLocalizations.of(context)!.couldNotLoadData + " " + API_BASE_URL + " " + (errorMsg != null ? errorMsg! : ""),
      buttonText: AppLocalizations.of(context)!.retry,
      buttonPressed: _loadPairingInitToken,
    );
  }

  Widget _loading() {
    return const SizedBox(
      width: 60,
      height: 60,
      child: CircularProgressIndicator(),
    );
  }

  Future<void> _launchUrl(String url) async {
    if (!await launchUrl(Uri.parse(url))) {
      throw Exception('Could not launch $url');
    }
  }
}
