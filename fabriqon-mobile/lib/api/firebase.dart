// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:fabrication/config.dart';
// import 'package:http/http.dart' as http;
//
// class FirebaseTokens {
//   FirebaseTokens();
//
//   static void initiate(String authenticationToken) async {
//     NotificationSettings settings = await FirebaseMessaging.instance.requestPermission(
//       alert: true,
//       announcement: false,
//       badge: true,
//       carPlay: false,
//       criticalAlert: false,
//       provisional: false,
//       sound: true,
//     );
//
//     if (settings.authorizationStatus != AuthorizationStatus.authorized) {
//       throw Exception("notifications_denied");
//     }
//
//     await FirebaseMessaging.instance.getToken().then((value) => _sendTokenToServer(authenticationToken, value!));
//
//     FirebaseMessaging.instance.onTokenRefresh
//         .listen((fcmToken) => _sendTokenToServer(authenticationToken, fcmToken))
//         .onError((err) {
//           // Error getting token.
//         });
//   }
//
//   static void _sendTokenToServer(String authenticationToken, String token) async {
//     var client = http.Client();
//     try {
//       var response = await client.post(Uri.parse('$API_BASE_URL/firebase/register-token/$token'),
//           headers: {"Content-Type": "application/json", "X-Auth": authenticationToken});
//       if (response.statusCode != 200) {
//         throw Exception("cannot_register_fcm_token");
//       }
//     } finally {
//       client.close();
//     }
//   }
//
// }
