import 'dart:convert';
import 'package:fabrication/config.dart';
import 'package:http/http.dart' as http;

import '../storage.dart';

class PairingInit {
  const PairingInit(
      {required this.employeeName, required this.confirmationToken});

  final String employeeName;
  final String confirmationToken;

  PairingInit.fromJson(Map<String, dynamic> json)
      : employeeName = json['employeeName'],
        confirmationToken = json['confirmationToken'];

  Map<String, dynamic> toJson() =>
      {'employeeName': employeeName, 'confirmationToken': confirmationToken};
}

class AccessToken {
  const AccessToken({required this.token});

  final String token;

  AccessToken.fromJson(Map<String, dynamic> json) : token = json['token'];

  Map<String, dynamic> toJson() => {'token': token};
}

class PairingApi {
  PairingApi();

  Future<PairingInit> initiate(String pairingToken) async {
    var client = http.Client();
    try {
      var response = await client.get(Uri.parse(
          '$API_BASE_URL/devices/pairing/initiate/$pairingToken'));
      return PairingInit.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
    } finally {
      client.close();
    }
  }

  Future<AccessToken> confirm(String confirmationToken) async {
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse('$API_BASE_URL/devices/pairing/confirm'),
          headers: {"Content-Type": "application/json"},
          body: json.encode({'token': confirmationToken})
      );
      return AccessToken.fromJson(jsonDecode(utf8.decode(response.bodyBytes)));
    } finally {
      client.close();
    }
  }

  Future<void> logout() async {
    var client = http.Client();
    try {
      var response = await client.post(
          Uri.parse('$API_BASE_URL/devices/pairing/remove'),
          headers: {"Content-Type": "application/json"},
          body: json.encode({'token': await Storage.read(DataKeys.API_TOKEN.name)})
      );
      if (response.statusCode != 200) {
        throw Exception("cannot_logout");
      }
    } finally {
      client.close();
    }
  }
}
