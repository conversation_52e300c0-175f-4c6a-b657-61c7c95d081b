import 'package:fabrication/config.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

import '../storage.dart';

class Entity {
  const Entity({required this.id, required this.name});

  final String id;
  final String name;

  Entity.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        name = json['name'];

  Map<String, dynamic> toJson() => {'id': id, 'name': name};
}

class ManufacturingMaterial {
  const ManufacturingMaterial({required this.id, required this.name, required this.quantity});

  final String id;
  final String name;
  final num quantity;

  ManufacturingMaterial.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        name = json['name'],
        quantity = json['quantity'];

  Map<String, dynamic> toJson() => {'id': id, 'name': name, 'quantity': quantity};
}

enum Status { TODO, IN_PROGRESS, DONE, STOPPED }

enum StatusReason { PAUSED, MISSING_MATERIAL, BROKEN_EQUIPMENT, EQUIPMENT_UNAVAILABLE }

class Task {
  const Task(
      {required this.id,
      required this.product,
      required this.status,
      required this.statusReason,
      required this.name,
      required this.number,
      required this.quantity,
      required this.measurementUnit,
      required this.notes,
      required this.assignedWorkstations,
      required this.assignedEmployees,
      required this.materials});

  final String id;
  final Entity product;
  final Status status;
  final StatusReason? statusReason;
  final String name;
  final String number;
  final num quantity;
  final Entity measurementUnit;
  final String? notes;
  final List<Entity> assignedWorkstations;
  final List<Entity> assignedEmployees;
  final List<ManufacturingMaterial> materials;

  Task.fromJson(Map<String, dynamic> json)
      : id = (json['id']),
        product = Entity.fromJson(json['product']),
        status = Status.values.firstWhere((e) => e.toString() == 'Status.${json["status"]}'),
        statusReason = json["statusReason"] != null ? StatusReason.values.firstWhere((e) => e.toString() == 'StatusReason.${json["statusReason"]}') : null,
        name = json['name'],
        number = json['number'],
        quantity = json['quantity'],
        measurementUnit = Entity.fromJson(json['measurementUnit']),
        notes = json['notes'],
        assignedWorkstations =
            (json['assignedWorkstations'] as List).map((e) => Entity.fromJson(e as Map<String, dynamic>)).toList(),
        assignedEmployees =
            (json['assignedEmployees'] as List).map((e) => Entity.fromJson(e as Map<String, dynamic>)).toList(),
        materials = ((json['materials'] ?? []) as List)
            .map((e) => ManufacturingMaterial.fromJson(e as Map<String, dynamic>))
            .toList();
}

class Api {
  Future<List<Task>> getTasksFromLocal(DateTime selectedDate) {
    return Future.value(List.of([
      Task(
          id: const Uuid().v4(),
          product: Entity(id: const Uuid().v4(), name: "Chair - Oak / Medium"),
          status: Status.STOPPED,
          statusReason: StatusReason.MISSING_MATERIAL,
          name: "Paint with lacquer",
          number: "MO-123 / 1 / 1",
          quantity: 10.0,
          measurementUnit: const Entity(id: "PIECE", name: "pcs"),
          notes: "Needs to be ready by May 10th",
          assignedWorkstations: List.of([Entity(id: const Uuid().v4(), name: "Painting 121")]),
          assignedEmployees: List.of([Entity(id: const Uuid().v4(), name: "Jerome Bell")]),
          materials: List.of([
            ManufacturingMaterial(id: const Uuid().v4(), name: "Screw 10", quantity: 10.0),
            ManufacturingMaterial(id: const Uuid().v4(), name: "Wood frame", quantity: 1),
            ManufacturingMaterial(id: const Uuid().v4(), name: "Wood legs", quantity: 10.0)
          ])),
      Task(
          id: const Uuid().v4(),
          product: Entity(id: const Uuid().v4(), name: "Table - Walnut / Large"),
          status: Status.TODO,
          statusReason: null,
          name: "Cut to dimensions",
          number: "MO-321 / 2 / 2",
          quantity: 10.0,
          measurementUnit: const Entity(id: "PIECE", name: "pcs"),
          notes: "Needs to be ready by May 10th",
          assignedWorkstations: List.of([Entity(id: const Uuid().v4(), name: "Cutting 232")]),
          assignedEmployees: List.of([Entity(id: const Uuid().v4(), name: "Jerome Bell")]),
          materials: List.of([
            ManufacturingMaterial(id: const Uuid().v4(), name: "Screw 10", quantity: 10.0),
            ManufacturingMaterial(id: const Uuid().v4(), name: "Wood frame", quantity: 1),
            ManufacturingMaterial(id: const Uuid().v4(), name: "Wood legs", quantity: 10.0)
          ])),
      Task(
          id: const Uuid().v4(),
          product: Entity(id: const Uuid().v4(), name: "Desk - Cherry / Small"),
          status: Status.DONE,
          statusReason: null,
          name: "Cut to dimensions",
          number: "MO-567 / 1 / 1",
          quantity: 10.0,
          measurementUnit: const Entity(id: "PIECE", name: "pcs"),
          notes: "Needs to be ready by May 10th",
          assignedWorkstations: List.of([Entity(id: const Uuid().v4(), name: "Cutting 232")]),
          assignedEmployees: List.of([Entity(id: const Uuid().v4(), name: "Jerome Bell")]),
          materials: List.of([
            ManufacturingMaterial(id: const Uuid().v4(), name: "Screw 10", quantity: 10.0),
            ManufacturingMaterial(id: const Uuid().v4(), name: "Wood frame", quantity: 1),
            ManufacturingMaterial(id: const Uuid().v4(), name: "Wood legs", quantity: 10.0)
          ])),
    ]));
  }

  Future<List<Task>> getTasks(DateTime selectedDate) async {
    var client = AuthenticatedClient(await Storage.read(DataKeys.API_TOKEN.name), http.Client());
    try {
      var day = DateFormat("yyyy-MM-dd").format(selectedDate);
      var response = await client.get(Uri.parse("$API_BASE_URL/manufacturing/tasks/list?day=$day"));
      return (jsonDecode(utf8.decode(response.bodyBytes)) as List)
          .map((e) => Task.fromJson(e as Map<String, dynamic>))
          .toList();
    } finally {
      client.close();
    }
  }

  Future<void> inProgress(String id) async {
    return _changeStatus(id, "in-progress");
  }

  Future<void> stopped(String id, StatusReason reason) async {
    return _changeStatus(id, "stopped", reason: reason.name);
  }

  Future<void> done(String id) async {
    return _changeStatus(id, "done");
  }

  Future<void> _changeStatus(String id, String pathFragment, {String? reason}) async {
    var client = AuthenticatedClient(await Storage.read(DataKeys.API_TOKEN.name), http.Client());
    try {
      var response = await client.post(Uri.parse("$API_BASE_URL/manufacturing/tasks/$id/$pathFragment?reason=$reason"));
      if (response.statusCode != 200) {
        throw Exception("cannot_update");
      }
    } finally {
      client.close();
    }
  }
}

class AuthenticatedClient extends http.BaseClient {
  final String token;
  final http.Client _inner;

  AuthenticatedClient(this.token, this._inner);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    request.headers['X-Auth'] = token;
    return _inner.send(request);
  }
}
