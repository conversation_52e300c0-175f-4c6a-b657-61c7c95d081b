'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import dynamic from 'next/dynamic';
import { isPwaInstalled } from '@/utils/pwaUtils';
import { Storage, DataKeys } from '@/utils/storage';

// Dynamically import client components to avoid hydration errors
const InstallPrompt = dynamic(() => import('@/components/InstallPrompt'), {
  ssr: false,
});

export default function Home() {
  const [isAppInstalled, setIsAppInstalled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const checkAppState = async () => {
      // Check for debug bypass
      const debugBypass = localStorage.getItem('fabrication_debug_bypass');
      const isDebugMode = searchParams.get('debug') === 'true';

      if (debugBypass === 'true' || isDebugMode) {
        try {
          await Storage.read(DataKeys.API_TOKEN);
          router.push('/main');
          return;
        } catch {
          router.push('/pairing/qr-scan');
          return;
        }
      }

      // Check if the app is installed
      const installed = isPwaInstalled();
      setIsAppInstalled(installed);

      // If installed, check if user is logged in
      if (installed) {
        try {
          await Storage.read(DataKeys.API_TOKEN);
          // User is logged in, redirect to main screen
          router.push('/main');
          return;
        } catch {
          // User is not logged in, redirect to QR scan
          router.push('/pairing/qr-scan');
          return;
        }
      }

      setIsLoading(false);
    };

    checkAppState();

    // Listen for display mode changes (e.g., when the app is installed)
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleChange = (e: MediaQueryListEvent) => {
      setIsAppInstalled(e.matches);
      if (e.matches) {
        // App was just installed, check login status
        checkAppState();
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [router, searchParams]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show install prompt if not installed
  if (!isAppInstalled) {
    return <InstallPrompt />;
  }

  // This should not be reached as we redirect above, but just in case
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
    </div>
  );
}
