'use client';

import dynamic from 'next/dynamic';
import { t } from '@/locales';

// Dynamically import to avoid SSR issues
const PairingQrScanner = dynamic(() => import('@/components/PairingQrScanner'), {
  ssr: false,
});

const QrScanPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-3">
          <h1 className="text-lg font-semibold text-gray-900">{t.loginWithQRCode}</h1>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-md mx-auto p-4 pt-8">
        <PairingQrScanner />
      </main>
    </div>
  );
};

export default QrScanPage;
