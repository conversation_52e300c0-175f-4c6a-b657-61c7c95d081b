'use client';

import dynamic from 'next/dynamic';
import { useParams } from 'next/navigation';

// Dynamically import to avoid SSR issues
const ConfirmationScreen = dynamic(() => import('@/components/ConfirmationScreen'), {
  ssr: false,
});

const ConfirmationPage = () => {
  const params = useParams();
  const token = params.token as string;

  return <ConfirmationScreen initiationToken={token} />;
};

export default ConfirmationPage;
