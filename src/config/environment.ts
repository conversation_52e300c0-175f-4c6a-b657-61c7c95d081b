// Environment configuration
export interface EnvironmentConfig {
  apiBaseUrl: string;
  environment: 'development' | 'test' | 'production';
}

// Default to development environment
const getEnvironmentConfig = (): EnvironmentConfig => {
  const env = process.env.NODE_ENV || 'development';
  const customApiUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

  // If custom API URL is provided, use it
  if (customApiUrl) {
    return {
      apiBaseUrl: customApiUrl,
      environment: env as 'development' | 'test' | 'production'
    };
  }

  // Default configurations based on environment
  switch (env) {
    case 'production':
      return {
        apiBaseUrl: 'https://fabriqon.app/api/fabrication',
        environment: 'production'
      };
    case 'test':
      return {
        apiBaseUrl: 'https://fabriqon.app/api/fabrication',
        environment: 'test'
      };
    default:
      return {
        apiBaseUrl: 'localhost:3000/api/fabrication',
        environment: 'development'
      };
  }
};

export const config = getEnvironmentConfig();
