// Storage utility for managing local data
export enum DataKeys {
  API_TOKEN = 'API_TOKEN',
  EMPLOYEE_NAME = 'EMPLOYEE_NAME'
}

export class Storage {
  static async read(key: string): Promise<string> {
    if (typeof window === 'undefined') {
      throw new Error('Storage not available on server side');
    }
    
    const value = localStorage.getItem(key);
    if (value === null) {
      throw new Error(`Value with key [${key}] not found`);
    }
    return value;
  }

  static async write(key: string, value: string): Promise<void> {
    if (typeof window === 'undefined') {
      throw new Error('Storage not available on server side');
    }
    
    localStorage.setItem(key, value);
  }

  static async delete(key: string): Promise<void> {
    if (typeof window === 'undefined') {
      throw new Error('Storage not available on server side');
    }
    
    localStorage.removeItem(key);
  }

  static async deleteAll(): Promise<void> {
    if (typeof window === 'undefined') {
      throw new Error('Storage not available on server side');
    }
    
    localStorage.clear();
  }

  static async exists(key: string): Promise<boolean> {
    if (typeof window === 'undefined') {
      return false;
    }
    
    return localStorage.getItem(key) !== null;
  }
}
