// API Types based on Flutter app
export interface Entity {
  id: string;
  name: string;
}

export interface ManufacturingMaterial {
  id: string;
  name: string;
  quantity: number;
}

export enum Status {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS', 
  DONE = 'DONE',
  STOPPED = 'STOPPED'
}

export enum StatusReason {
  PAUSED = 'PAUSED',
  MISSING_MATERIAL = 'MISSING_MATERIAL',
  BROKEN_EQUIPMENT = 'BROKEN_EQUIPMENT',
  EQUIPMENT_UNAVAILABLE = 'EQUIPMENT_UNAVAILABLE'
}

export interface Task {
  id: string;
  product: Entity;
  status: Status;
  statusReason?: StatusReason;
  name: string;
  number: string;
  quantity: number;
  measurementUnit: Entity;
  notes?: string;
  assignedWorkstations: Entity[];
  assignedEmployees: Entity[];
  materials: ManufacturingMaterial[];
}

export interface PairingInit {
  employeeName: string;
  confirmationToken: string;
}

export interface AccessToken {
  token: string;
}

export interface ApiError {
  message: string;
  status?: number;
}
