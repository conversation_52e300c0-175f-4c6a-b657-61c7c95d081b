// HTTP client with authentication
import { config } from '@/config/environment';
import { Storage, DataKeys } from '@/utils/storage';

export class AuthenticatedClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = config.apiBaseUrl;
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    try {
      const token = await Storage.read(DataKeys.API_TOKEN);
      return {
        'Content-Type': 'application/json',
        'X-Auth': token
      };
    } catch {
      return {
        'Content-Type': 'application/json'
      };
    }
  }

  async get(endpoint: string): Promise<Response> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  }

  async post(endpoint: string, data?: any): Promise<Response> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: data ? JSON.stringify(data) : undefined
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  }

  async put(endpoint: string, data?: any): Promise<Response> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers,
      body: data ? JSON.stringify(data) : undefined
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  }

  async delete(endpoint: string): Promise<Response> {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response;
  }
}
