// Pairing API - Device authentication
import { config } from '@/config/environment';
import { PairingInit, AccessToken } from '@/types/api';
import { Storage, DataKeys } from '@/utils/storage';

export class PairingApi {
  private baseUrl: string;

  constructor() {
    this.baseUrl = config.apiBaseUrl;
  }



  async initiate(pairingToken: string): Promise<PairingInit> {
    const response = await fetch(
      `${this.baseUrl}/devices/pairing/initiate/${pairingToken}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to initiate pairing: ${response.status}`);
    }

    const data = await response.json();
    return {
      employeeName: data.employeeName,
      confirmationToken: data.confirmationToken
    };
  }

  async confirm(confirmationToken: string): Promise<AccessToken> {
    const response = await fetch(
      `${this.baseUrl}/devices/pairing/confirm`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token: confirmationToken })
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to confirm pairing: ${response.status}`);
    }

    const data = await response.json();
    return {
      token: data.token
    };
  }

  async logout(): Promise<void> {
    try {
      const token = await Storage.read(DataKeys.API_TOKEN);
      const response = await fetch(
        `${this.baseUrl}/devices/pairing/remove`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ token })
        }
      );

      if (!response.ok) {
        throw new Error('Cannot logout');
      }
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }
}
