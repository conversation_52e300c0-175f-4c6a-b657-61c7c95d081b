// Manufacturing API - Task management
import { AuthenticatedClient } from './client';
import { Task, Status, StatusReason } from '@/types/api';

export class ManufacturingApi {
  private client: AuthenticatedClient;

  constructor() {
    this.client = new AuthenticatedClient();
  }

  async getTasks(selectedDate: Date): Promise<Task[]> {
    const day = selectedDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const response = await this.client.get(`/manufacturing/tasks/list?day=${day}`);
    const data = await response.json();
    
    return data.map((item: any) => ({
      id: item.id,
      product: {
        id: item.product.id,
        name: item.product.name
      },
      status: item.status as Status,
      statusReason: item.statusReason as StatusReason | undefined,
      name: item.name,
      number: item.number,
      quantity: item.quantity,
      measurementUnit: {
        id: item.measurementUnit.id,
        name: item.measurementUnit.name
      },
      notes: item.notes,
      assignedWorkstations: item.assignedWorkstations || [],
      assignedEmployees: item.assignedEmployees || [],
      materials: item.materials || []
    }));
  }

  async setTaskInProgress(taskId: string): Promise<void> {
    await this.changeStatus(taskId, 'in-progress');
  }

  async setTaskStopped(taskId: string, reason: StatusReason): Promise<void> {
    await this.changeStatus(taskId, 'stopped', reason.toLowerCase());
  }

  async setTaskDone(taskId: string): Promise<void> {
    await this.changeStatus(taskId, 'done');
  }

  private async changeStatus(taskId: string, pathFragment: string, reason?: string): Promise<void> {
    const endpoint = `/manufacturing/tasks/${taskId}/${pathFragment}${reason ? `?reason=${reason}` : ''}`;
    const response = await this.client.post(endpoint);
    
    if (!response.ok) {
      throw new Error('Cannot update task status');
    }
  }

  // Mock data for development/testing
  async getMockTasks(): Promise<Task[]> {
    return [
      {
        id: '1',
        product: { id: '1', name: 'Chair - Oak / Medium' },
        status: Status.STOPPED,
        statusReason: StatusReason.MISSING_MATERIAL,
        name: 'Paint with lacquer',
        number: 'MO-123 / 1 / 1',
        quantity: 10,
        measurementUnit: { id: 'PIECE', name: 'pcs' },
        notes: 'Needs to be ready by May 10th',
        assignedWorkstations: [{ id: '1', name: 'Painting 121' }],
        assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
        materials: [
          { id: '1', name: 'Screw 10', quantity: 10 },
          { id: '2', name: 'Wood frame', quantity: 1 },
          { id: '3', name: 'Wood legs', quantity: 10 }
        ]
      },
      {
        id: '2',
        product: { id: '2', name: 'Table - Walnut / Large' },
        status: Status.TODO,
        name: 'Cut to dimensions',
        number: 'MO-321 / 2 / 2',
        quantity: 10,
        measurementUnit: { id: 'PIECE', name: 'pcs' },
        notes: 'Needs to be ready by May 10th',
        assignedWorkstations: [{ id: '2', name: 'Cutting 232' }],
        assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
        materials: [
          { id: '1', name: 'Screw 10', quantity: 10 },
          { id: '2', name: 'Wood frame', quantity: 1 },
          { id: '3', name: 'Wood legs', quantity: 10 }
        ]
      },
      {
        id: '3',
        product: { id: '3', name: 'Desk - Cherry / Small' },
        status: Status.DONE,
        name: 'Cut to dimensions',
        number: 'MO-567 / 1 / 1',
        quantity: 10,
        measurementUnit: { id: 'PIECE', name: 'pcs' },
        notes: 'Needs to be ready by May 10th',
        assignedWorkstations: [{ id: '2', name: 'Cutting 232' }],
        assignedEmployees: [{ id: '1', name: 'Jerome Bell' }],
        materials: [
          { id: '1', name: 'Screw 10', quantity: 10 },
          { id: '2', name: 'Wood frame', quantity: 1 },
          { id: '3', name: 'Wood legs', quantity: 10 }
        ]
      }
    ];
  }
}
