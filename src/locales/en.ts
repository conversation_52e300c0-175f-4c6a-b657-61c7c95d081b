export const en = {
  fabricationApp: "Fabrication App",
  loginWithQRCode: "Log in with QR code",
  accountConfirmation: "Account confirmation",
  initiationTokenIsNull: "Initiation token is null",
  confirmationOfTAndC: "By confirming your account you agree to our Privacy Policy and Terms and Conditions",
  privacyPolicy: "Privacy Policy",
  tAndC: "Terms and Conditions",
  confirmAccount: "Confirm account",
  couldNotLoadData: "Could not load data",
  retry: "Retry",
  completedTasks: "Completed tasks",
  menu: "Menu",
  logout: "Log out",
  noPermission: "No permission",
  notes: "Notes",
  start: "Start",
  stop: "Stop",
  resume: "Resume",
  markCompleted: "Mark as completed",
  materials: "Materials",
  neededQuantity: "Needed quantity",
  cantUpdateTask: "Can't update task!",
  paused: "Paused",
  missingMaterial: "Missing material",
  brokenEquipment: "Broken equipment",
  equipmentUnavailable: "Equipment unavailable",
  measurementUnitPiece: "pcs",
  
  // Additional PWA specific translations
  installApp: "Install App",
  installPromptTitle: "Install Fabrication App",
  installPromptDescription: "Install this app on your device for the best experience",
  scanQRCode: "Scan QR Code",
  scanningForDevices: "Scanning for devices...",
  cameraPermissionRequired: "Camera permission is required to scan QR codes",
  loading: "Loading...",
  error: "Error",
  today: "Today",
  yesterday: "Yesterday",
  tomorrow: "Tomorrow",
  noTasksForDate: "No tasks for this date",
  taskUpdated: "Task updated successfully",
  confirmLogout: "Are you sure you want to log out?",
  cancel: "Cancel",
  confirm: "Confirm"
};
