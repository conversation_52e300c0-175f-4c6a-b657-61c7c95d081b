'use client';

import { useEffect, useRef, useState } from 'react';
import { Html5Qrcode } from 'html5-qrcode';
import { useRouter } from 'next/navigation';
import { Storage, DataKeys } from '@/utils/storage';
import { t } from '@/locales';

interface PairingQrScannerProps {
  onScanSuccess?: (token: string) => void;
}

const PairingQrScanner = ({ onScanSuccess }: PairingQrScannerProps) => {
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [availableCameras, setAvailableCameras] = useState<any[]>([]);
  const [selectedCamera, setSelectedCamera] = useState<string>('');
  const [manualToken, setManualToken] = useState('');
  const [showManualInput, setShowManualInput] = useState(false);

  const scannerRef = useRef<Html5Qrcode | null>(null);
  const router = useRouter();
  const scannerContainerId = 'pairing-qr-reader';

  // Check if already logged in
  useEffect(() => {
    const checkLoggedIn = async () => {
      try {
        await Storage.read(DataKeys.API_TOKEN);
        router.push('/main');
      } catch {
        // Not logged in, continue with QR scanning
      }
    };
    
    checkLoggedIn();
  }, [router]);

  // Initialize scanner
  useEffect(() => {
    if (typeof window !== 'undefined') {
      scannerRef.current = new Html5Qrcode(scannerContainerId);

      // Get available cameras
      Html5Qrcode.getCameras()
        .then(devices => {
          if (devices && devices.length) {
            setAvailableCameras(devices);

            // Try to find back camera
            const backCamera = devices.find(device =>
              device.label.toLowerCase().includes('back') ||
              device.label.toLowerCase().includes('environment') ||
              device.label.toLowerCase().includes('rear')
            );

            if (backCamera) {
              setSelectedCamera(backCamera.id);
            } else {
              setSelectedCamera(devices[devices.length - 1].id);
            }
          }
        })
        .catch(err => {
          setError(t.cameraPermissionRequired);
          console.error('Error getting cameras:', err);
        });
    }

    return () => {
      if (scannerRef.current && isScanning) {
        scannerRef.current.stop()
          .catch(err => console.error('Error stopping scanner:', err));
      }
    };
  }, [isScanning]);

  const startScanning = async () => {
    if (!scannerRef.current || !selectedCamera) return;

    setIsLoading(true);
    setError(null);

    try {
      await scannerRef.current.start(
        selectedCamera,
        {
          fps: 10,
          qrbox: { width: 250, height: 250 }
        },
        (decodedText) => {
          handleScanSuccess(decodedText);
        },
        (errorMessage) => {
          // Ignore scanning errors (they happen frequently)
          console.log('Scan error:', errorMessage);
        }
      );

      setIsScanning(true);
    } catch (err) {
      setError(`${t.error}: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  const stopScanning = async () => {
    if (!scannerRef.current || !isScanning) return;

    try {
      await scannerRef.current.stop();
      setIsScanning(false);
    } catch (err) {
      setError(`${t.error}: ${err}`);
    }
  };

  const handleScanSuccess = (token: string) => {
    stopScanning();
    if (onScanSuccess) {
      onScanSuccess(token);
    } else {
      // Navigate to confirmation page
      router.push(`/pairing/confirmation/${token}`);
    }
  };

  const handleCameraChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCamera(e.target.value);
  };

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (manualToken.trim()) {
      handleScanSuccess(manualToken.trim());
    }
  };

  return (
    <div className="flex flex-col items-center w-full max-w-md mx-auto">
      <div className="w-full mb-4">
        <label htmlFor="camera-select" className="block text-sm font-medium mb-2 text-gray-900">
          Select Camera
        </label>
        <select
          id="camera-select"
          value={selectedCamera}
          onChange={handleCameraChange}
          className="w-full p-2 border border-gray-300 rounded-md bg-white text-gray-900"
          disabled={isScanning || isLoading}
        >
          {availableCameras.length === 0 && (
            <option value="" className="text-gray-900">No cameras found</option>
          )}
          {availableCameras.map((camera) => (
            <option key={camera.id} value={camera.id} className="text-gray-900">
              {camera.label || `Camera ${camera.id}`}
            </option>
          ))}
        </select>
      </div>

      {/* QR Scanner Container */}
      <div
        id={scannerContainerId}
        className="w-full h-64 bg-gray-100 rounded-lg overflow-hidden mb-4 flex items-center justify-center"
      >
        {!isScanning && !isLoading && (
          <div className="text-gray-700 text-center">
            <p className="text-gray-700 font-medium">{t.scanQRCode}</p>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="space-y-4 w-full">
        <div className="flex gap-4">
          {!isScanning && (
            <button
              onClick={startScanning}
              disabled={isLoading || !selectedCamera}
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 font-medium"
            >
              {isLoading ? t.loading : t.scanQRCode}
            </button>
          )}

          {isScanning && (
            <button
              onClick={stopScanning}
              className="flex-1 bg-red-600 text-white py-3 px-4 rounded-md hover:bg-red-700 font-medium"
            >
              {t.stop}
            </button>
          )}
        </div>

        {/* Manual Token Input Toggle */}
        <div className="text-center">
          <button
            onClick={() => setShowManualInput(!showManualInput)}
            className="text-sm text-blue-600 hover:text-blue-800 underline font-medium"
          >
            {showManualInput ? 'Hide manual input' : 'Enter token manually'}
          </button>
        </div>

        {/* Manual Token Input */}
        {showManualInput && (
          <form onSubmit={handleManualSubmit} className="space-y-3">
            <div>
              <label htmlFor="manual-token" className="block text-sm font-medium mb-1 text-gray-900">
                Pairing Token
              </label>
              <input
                id="manual-token"
                type="text"
                value={manualToken}
                onChange={(e) => setManualToken(e.target.value)}
                placeholder="Enter pairing token..."
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500"
              />
            </div>
            <button
              type="submit"
              disabled={!manualToken.trim()}
              className="w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 font-medium"
            >
              Pair with Token
            </button>
          </form>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md w-full">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md w-full">
        <p className="text-blue-800 text-sm text-center font-medium">
          {t.scanningForDevices}
        </p>
      </div>
    </div>
  );
};

export default PairingQrScanner;
