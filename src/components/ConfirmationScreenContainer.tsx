import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { PairingApi } from '../api/pairing';
import { PairingInit } from '../types/api';
import { Storage, DataKeys } from '../utils/storage';
import { t } from '../locales';
import ConfirmationScreen from './ConfirmationScreen';

interface ConfirmationScreenContainerProps {
  initiationToken: string;
}

const ConfirmationScreenContainer = ({ initiationToken }: ConfirmationScreenContainerProps) => {
  const [pairingInit, setPairingInit] = useState<PairingInit | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConfirming, setIsConfirming] = useState(false);
  
  const router = useRouter();
  const pairingApi = new PairingApi();

  useEffect(() => {
    if (!initiationToken) {
      setError(t.initiationTokenIsNull);
      setIsLoading(false);
      return;
    }

    loadPairingInit();
  }, [initiationToken]);

  const loadPairingInit = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await pairingApi.initiate(initiationToken);
      setPairingInit(result);
    } catch (err) {
      setError(`${t.couldNotLoadData}: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirm = async () => {
    if (!pairingInit) return;

    setIsConfirming(true);
    try {
      const accessToken = await pairingApi.confirm(pairingInit.confirmationToken);
      
      // Store credentials
      await Storage.write(DataKeys.API_TOKEN, accessToken.token);
      await Storage.write(DataKeys.EMPLOYEE_NAME, pairingInit.employeeName);
      
      // Navigate to main screen
      router.push('/main');
    } catch (err) {
      setError(`${t.error}: ${err}`);
      // On error, go back to QR scan
      setTimeout(() => {
        router.push('/pairing/qr-scan');
      }, 2000);
    } finally {
      setIsConfirming(false);
    }
  };

  const handleOpenPrivacyPolicy = () => {
    window.open('https://fabriqon-app-prod.web.app/privacy_policy_en.html', '_blank');
  };

  const handleOpenTermsAndConditions = () => {
    window.open('https://fabriqon-app-prod.web.app/t_and_c_en.html', '_blank');
  };

  return (
    <ConfirmationScreen
      initiationToken={initiationToken}
      pairingInit={pairingInit}
      isLoading={isLoading}
      error={error}
      isConfirming={isConfirming}
      onRetry={loadPairingInit}
      onConfirm={handleConfirm}
      onOpenPrivacyPolicy={handleOpenPrivacyPolicy}
      onOpenTermsAndConditions={handleOpenTermsAndConditions}
    />
  );
};

export default ConfirmationScreenContainer;
