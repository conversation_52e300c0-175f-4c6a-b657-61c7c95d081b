import { useEffect, useRef, useState } from 'react';
import { Html5Qrcode } from 'html5-qrcode';
import { useRouter } from 'next/router';
import { Storage, DataKeys } from '../utils/storage';
import { t } from '../locales';
import PairingQrScanner from './PairingQrScanner';

interface PairingQrScannerContainerProps {
  onScanSuccess?: (token: string) => void;
}

const PairingQrScannerContainer = ({ onScanSuccess }: PairingQrScannerContainerProps) => {
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [availableCameras, setAvailableCameras] = useState<any[]>([]);
  const [selectedCamera, setSelectedCamera] = useState<string>('');
  const [manualToken, setManualToken] = useState('');
  const [showManualInput, setShowManualInput] = useState(false);
  
  const scannerRef = useRef<Html5Qrcode | null>(null);
  const router = useRouter();
  const scannerContainerId = 'pairing-qr-reader';

  // Check if already logged in
  useEffect(() => {
    const checkLoggedIn = async () => {
      try {
        await Storage.read(DataKeys.API_TOKEN);
        router.push('/main');
      } catch {
        // Not logged in, continue with QR scanning
      }
    };
    
    checkLoggedIn();
  }, [router]);

  // Initialize scanner
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Add a small delay to ensure DOM is ready
      setTimeout(() => {
        try {
          scannerRef.current = new Html5Qrcode(scannerContainerId);

          // Get available cameras
          Html5Qrcode.getCameras()
            .then(devices => {
          if (devices && devices.length) {
            setAvailableCameras(devices);

            // Try to find back camera
            const backCamera = devices.find(device =>
              device.label.toLowerCase().includes('back') ||
              device.label.toLowerCase().includes('environment') ||
              device.label.toLowerCase().includes('rear')
            );

            if (backCamera) {
              setSelectedCamera(backCamera.id);
            } else {
              setSelectedCamera(devices[devices.length - 1].id);
            }
          } else {
            // No cameras available, but permission was granted
            setAvailableCameras([]);
            setShowManualInput(true); // Automatically show manual input
            setError('No cameras found on this device. Please enter the pairing token manually below.');
          }
        })
            .catch(err => {
              console.error('Error getting cameras:', err);
              setAvailableCameras([]);

              // Always show manual input when camera fails
              setShowManualInput(true);

              // Check if it's a permission error or no cameras available
              if (err.name === 'NotAllowedError' || err.message.includes('permission')) {
                setError('Camera permission denied. Please enter the pairing token manually below.');
              } else if (err.name === 'NotReadableError' || err.message.includes('videosource') || err.message.includes('allocate')) {
                setError('Camera is not available (may be in use by another app). Please enter the pairing token manually below.');
              } else if (err.name === 'NotFoundError' || err.message.includes('not found')) {
                setError('No cameras found on this device. Please enter the pairing token manually below.');
              } else {
                setError('Camera access failed. Please enter the pairing token manually below.');
              }
            });
        } catch (initError) {
          console.error('Error initializing camera scanner:', initError);
          setAvailableCameras([]);
          setShowManualInput(true);
          setError('Camera initialization failed. Please enter the pairing token manually below.');
        }
      }, 100); // Small delay to ensure DOM is ready
    }

    return () => {
      if (scannerRef.current) {
        try {
          if (isScanning) {
            scannerRef.current.stop()
              .catch(err => console.error('Error stopping scanner:', err));
          }
        } catch (err) {
          console.error('Error during cleanup:', err);
        }
      }
    };
  }, [isScanning]);

  const startScanning = async () => {
    if (!scannerRef.current || !selectedCamera) {
      setError('No camera selected. Please select a camera or enter the token manually.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await scannerRef.current.start(
        selectedCamera,
        {
          fps: 10,
          qrbox: { width: 250, height: 250 }
        },
        (decodedText) => {
          handleScanSuccess(decodedText);
        },
        (errorMessage) => {
          // Ignore scanning errors (they happen frequently during normal operation)
          console.log('Scan error:', errorMessage);
        }
      );

      setIsScanning(true);
    } catch (err: any) {
      console.error('Error starting camera:', err);

      // Provide user-friendly error messages based on error type
      if (err.name === 'NotAllowedError') {
        setError('Camera permission denied. Please allow camera access and try again, or enter the token manually.');
      } else if (err.name === 'NotReadableError') {
        setError('Camera is not available or is being used by another application. Please close other apps using the camera or enter the token manually.');
      } else if (err.name === 'NotFoundError') {
        setError('Selected camera not found. Please try a different camera or enter the token manually.');
      } else if (err.name === 'OverconstrainedError') {
        setError('Camera does not support the required settings. Please try a different camera or enter the token manually.');
      } else {
        setError('Failed to start camera. Please try again or enter the token manually.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const stopScanning = async () => {
    if (!scannerRef.current || !isScanning) return;

    try {
      await scannerRef.current.stop();
      setIsScanning(false);
    } catch (err) {
      setError(`${t.error}: ${err}`);
    }
  };

  const handleScanSuccess = (token: string) => {
    stopScanning();
    if (onScanSuccess) {
      onScanSuccess(token);
    } else {
      // Navigate to confirmation page
      router.push(`/pairing/confirmation/${token}`);
    }
  };

  const handleCameraChange = (cameraId: string) => {
    setSelectedCamera(cameraId);
  };

  const handleManualTokenChange = (token: string) => {
    setManualToken(token);
  };

  const handleToggleManualInput = () => {
    setShowManualInput(!showManualInput);
  };

  const handleManualSubmit = (token: string) => {
    handleScanSuccess(token);
  };

  return (
    <PairingQrScanner
      isScanning={isScanning}
      error={error}
      isLoading={isLoading}
      availableCameras={availableCameras}
      selectedCamera={selectedCamera}
      manualToken={manualToken}
      showManualInput={showManualInput}
      onScanSuccess={handleScanSuccess}
      onStartScanning={startScanning}
      onStopScanning={stopScanning}
      onCameraChange={handleCameraChange}
      onManualTokenChange={handleManualTokenChange}
      onToggleManualInput={handleToggleManualInput}
      onManualSubmit={handleManualSubmit}
    />
  );
};

export default PairingQrScannerContainer;
