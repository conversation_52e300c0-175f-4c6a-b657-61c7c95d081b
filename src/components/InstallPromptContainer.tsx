import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { isPwaInstallable, setupInstallHandler, debugInstallPwa } from '../utils/pwaUtils';
import InstallPrompt from './InstallPrompt';

const InstallPromptContainer = () => {
  const [, setIsInstallable] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installError, setInstallError] = useState<string | null>(null);
  
  const router = useRouter();

  useEffect(() => {
    // Check for debug bypass in localStorage
    const debugBypass = localStorage.getItem('fabrication_debug_bypass');
    if (debugBypass === 'true') {
      router.push('/pairing/qr-scan');
      return;
    }

    // Set up the install handler
    setupInstallHandler();

    // Check if the app is installable
    const checkInstallable = () => {
      const canInstall = isPwaInstallable();
      console.log('App installable:', canInstall);
      setIsInstallable(canInstall);
    };

    // Check immediately
    checkInstallable();

    // Listen for changes in installability
    const handleInstallable = () => {
      console.log('pwaInstallable event received');
      setIsInstallable(true);
    };

    window.addEventListener('pwaInstallable', handleInstallable);

    // Check again after a short delay (some browsers need time)
    const timer = setTimeout(checkInstallable, 1000);

    return () => {
      window.removeEventListener('pwaInstallable', handleInstallable);
      clearTimeout(timer);
    };
  }, [router]);

  const handleInstallClick = async () => {
    setInstallError(null);
    setIsInstalling(true);

    try {
      console.log('Attempting to install...');
      debugInstallPwa();

      setTimeout(() => {
        setIsInstalling(false);
      }, 3000);
    } catch (error) {
      console.error('Installation failed:', error);
      setInstallError('Installation failed. Please try again or use the browser menu.');
      setIsInstalling(false);
    }
  };

  const handleDebugBypass = () => {
    localStorage.setItem('fabrication_debug_bypass', 'true');
    router.push('/pairing/qr-scan');
  };

  return (
    <InstallPrompt
      isInstalling={isInstalling}
      installError={installError}
      onInstallClick={handleInstallClick}
      onDebugBypass={handleDebugBypass}
    />
  );
};

export default InstallPromptContainer;
