import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { Storage, DataKeys } from '../utils/storage';
import { ManufacturingApi } from '../api/manufacturing';
import { PairingApi } from '../api/pairing';
import { Task } from '../types/api';
import { t } from '../locales';
import MainScreen from './MainScreen';

const MainScreenContainer = () => {
  const [employeeName, setEmployeeName] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMenu, setShowMenu] = useState(false);
  
  const router = useRouter();
  const manufacturingApi = new ManufacturingApi();
  const pairingApi = new PairingApi();

  useEffect(() => {
    checkEmployee();
    loadTasks();
  }, [selectedDate]);

  const checkEmployee = async () => {
    try {
      const name = await Storage.read(DataKeys.EMPLOYEE_NAME);
      setEmployeeName(name);
    } catch {
      router.push('/pairing/qr-scan');
    }
  };

  const loadTasks = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // For development, use mock data. In production, use real API
      const taskList = await manufacturingApi.getMockTasks();
      setTasks(taskList);
    } catch (err) {
      setError(t.couldNotLoadData);
      console.error('Error loading tasks:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreviousDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() - 1);
    setSelectedDate(newDate);
  };

  const handleNextDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + 1);
    setSelectedDate(newDate);
  };

  const handleToggleMenu = () => {
    setShowMenu(!showMenu);
  };

  const handleLogout = async () => {
    if (confirm(t.confirmLogout)) {
      try {
        await pairingApi.logout();
        await Storage.deleteAll();
        router.push('/pairing/qr-scan');
      } catch (err) {
        console.error('Logout error:', err);
        // Even if logout fails, clear local storage and redirect
        await Storage.deleteAll();
        router.push('/pairing/qr-scan');
      }
    }
  };

  return (
    <MainScreen
      employeeName={employeeName}
      selectedDate={selectedDate}
      tasks={tasks}
      isLoading={isLoading}
      error={error}
      showMenu={showMenu}
      onPreviousDay={handlePreviousDay}
      onNextDay={handleNextDay}
      onToggleMenu={handleToggleMenu}
      onLogout={handleLogout}
      onTaskUpdate={loadTasks}
    />
  );
};

export default MainScreenContainer;
