import { useState } from 'react';
import { Task, StatusReason } from '../types/api';
import { ManufacturingApi } from '../api/manufacturing';
import { t } from '../locales';
import TaskCard from './TaskCard';

interface TaskCardContainerProps {
  task: Task;
  onTaskUpdate: () => void;
}

const TaskCardContainer = ({ task, onTaskUpdate }: TaskCardContainerProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showStopOptions, setShowStopOptions] = useState(false);
  
  const manufacturingApi = new ManufacturingApi();

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleTaskInProgress = async (taskId: string) => {
    setIsUpdating(true);
    try {
      await manufacturingApi.setTaskInProgress(taskId);
      onTaskUpdate();
    } catch (error) {
      alert(t.cantUpdateTask);
      console.error('Error updating task:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTaskDone = async (taskId: string) => {
    setIsUpdating(true);
    try {
      await manufacturingApi.setTaskDone(taskId);
      onTaskUpdate();
    } catch (error) {
      alert(t.cantUpdateTask);
      console.error('Error updating task:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTaskStopped = async (taskId: string, reason: StatusReason) => {
    setIsUpdating(true);
    setShowStopOptions(false);
    try {
      await manufacturingApi.setTaskStopped(taskId, reason);
      onTaskUpdate();
    } catch (error) {
      alert(t.cantUpdateTask);
      console.error('Error updating task:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleShowStopOptions = () => {
    setShowStopOptions(true);
  };

  const handleHideStopOptions = () => {
    setShowStopOptions(false);
  };

  return (
    <TaskCard
      task={task}
      isExpanded={isExpanded}
      isUpdating={isUpdating}
      showStopOptions={showStopOptions}
      onTaskUpdate={onTaskUpdate}
      onToggleExpanded={handleToggleExpanded}
      onTaskInProgress={handleTaskInProgress}
      onTaskDone={handleTaskDone}
      onTaskStopped={handleTaskStopped}
      onShowStopOptions={handleShowStopOptions}
      onHideStopOptions={handleHideStopOptions}
    />
  );
};

export default TaskCardContainer;
