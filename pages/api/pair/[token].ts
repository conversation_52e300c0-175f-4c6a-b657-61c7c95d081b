import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = req.query;
  
  if (!token || typeof token !== 'string') {
    return res.status(400).json({ error: 'Token is required' });
  }

  // Redirect to the confirmation page
  const confirmationUrl = `/pairing/confirmation/${token}`;
  
  return res.redirect(302, confirmationUrl);
}
