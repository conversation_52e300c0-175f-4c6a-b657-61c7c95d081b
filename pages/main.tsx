import Head from 'next/head';
import dynamic from 'next/dynamic';

// Dynamically import to avoid SSR issues
const MainScreenContainer = dynamic(() => import('../src/components/MainScreenContainer'), {
  ssr: false,
});

export default function MainPage() {
  return (
    <>
      <Head>
        <title>Main - Fabrication App</title>
        <meta name="description" content="Manufacturing task management dashboard" />
      </Head>
      <MainScreenContainer />
    </>
  );
}
