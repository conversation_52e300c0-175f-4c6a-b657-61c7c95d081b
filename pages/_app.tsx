import type { AppProps } from 'next/app';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import '../styles/globals.css';
import Providers from '../src/components/Providers';
import ClientLayout from '../src/components/ClientLayout';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function App({ Component, pageProps }: AppProps) {
  return (
    <div className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
      <Providers>
        <ClientLayout>
          <Component {...pageProps} />
        </ClientLayout>
      </Providers>
    </div>
  );
}
