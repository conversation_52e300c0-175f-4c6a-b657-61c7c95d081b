import Head from 'next/head';
import dynamic from 'next/dynamic';
import { t } from '../../src/locales';

// Dynamically import to avoid SSR issues
const PairingQrScannerContainer = dynamic(() => import('../../src/components/PairingQrScannerContainer'), {
  ssr: false,
});

export default function QrScanPage() {
  return (
    <>
      <Head>
        <title>QR Scan - Fabrication App</title>
        <meta name="description" content="Scan QR code for device pairing" />
      </Head>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 py-3">
            <h1 className="text-lg font-semibold text-gray-900">{t.loginWithQRCode}</h1>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-md mx-auto p-4 pt-8">
          <PairingQrScannerContainer />
        </main>
      </div>
    </>
  );
}
