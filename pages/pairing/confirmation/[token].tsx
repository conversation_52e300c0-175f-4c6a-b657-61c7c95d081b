import { useRouter } from 'next/router';
import Head from 'next/head';
import dynamic from 'next/dynamic';

// Dynamically import to avoid SSR issues
const ConfirmationScreenContainer = dynamic(() => import('../../../src/components/ConfirmationScreenContainer'), {
  ssr: false,
});

export default function ConfirmationPage() {
  const router = useRouter();
  const { token } = router.query;

  return (
    <>
      <Head>
        <title>Confirmation - Fabrication App</title>
        <meta name="description" content="Confirm device pairing" />
      </Head>
      <ConfirmationScreenContainer initiationToken={token as string} />
    </>
  );
}
